const REQUEST = require('../utils/request.js')
const util = require('@/utils/util')
// 房屋管理API接口
// 基于最新API文档：/users-api/v1/member/room


function getHouseById(id) {

  return REQUEST.request('/users-api/v1/member/room?id=' + id, 'GET', {}, true)
}

/**
 * 分页查询我的房屋列表
 * @param {Object} query - 查询参数
 * @param {number} query.pageNum - 页码，默认1
 * @param {number} query.pageSize - 每页大小，默认10
 * @param {number} query.communityId - 小区ID（可选）
 * @returns {Promise} 房屋列表
 */
function getHouseList(query = {}) {


  const defaultQuery = {
    pageNum: 1,
    pageSize: 500,
    communityId: wx.getStorageSync('selectedCommunity').id,
    residentId: wx.getStorageSync('userInfo').residentId
  }

  return REQUEST.request('/users-api/v1/member/room/page', 'GET', defaultQuery, true)
}

/**
 * 新增我的房屋
 * @param {Object} roomData - 房屋数据
 * @param {number} roomData.buildingId - 楼栋ID
 * @param {number} roomData.roomId - 房间ID
 * @param {string} roomData.residentType - 角色：owner(业主)、tenant(租户)、family(家庭成员)
 * @returns {Promise} 新增结果
 */
function addHouse(roomData) {
  // 参数验证
  if (!roomData) {
    return Promise.reject(new Error('房屋数据不能为空'))
  }

  if (!roomData.buildingId) {
    return Promise.reject(new Error('楼栋ID不能为空'))
  }

  if (!roomData.roomId) {
    return Promise.reject(new Error('房间ID不能为空'))
  }

  if (!roomData.residentType) {
    return Promise.reject(new Error('角色不能为空'))
  }

  // 构建房屋数据，严格按照接口文档
  const requestData = {
    buildingId: roomData.buildingId,
    roomId: roomData.roomId,
    residentType: roomData.residentType
  }

  return REQUEST.request('/users-api/v1/member/room', 'POST', requestData, true)
}

/**
 * 更新我的房屋
 * @param {Object} roomData - 房屋数据
 * @param {number} roomData.id - 房屋记录ID
 * @param {string} roomData.residentType - 角色：owner(业主)、tenant(租户)、family(家庭成员)
 * @returns {Promise} 更新结果
 */
function updateHouse(roomData) {
  if (!roomData || !roomData.id) {
    return Promise.reject(new Error('房屋ID不能为空'))
  }

  // 构建更新数据，严格按照接口文档
  const updateData = {
    id: roomData.id,
    residentType: roomData.residentType
  }

  return REQUEST.request('/users-api/v1/member/room', 'PUT', updateData, true)
}

/**
 * 删除我的房屋
 * @param {number} id - 房屋记录ID
 * @returns {Promise} 删除结果
 */
function deleteHouse(id) {
  if (!id) {
    return Promise.reject(new Error('房屋ID不能为空'))
  }

  return REQUEST.request(`/users-api/v1/member/room?id=` + id, 'DELETE', {}, true)
}

/**
 * 获取小区的楼栋列表
 * @param {number} communityId - 小区ID
 * @returns {Promise} 楼栋列表
 */
function getBuildingsByCommunity(params) {

  return REQUEST.request('/users-api/v1/community/building/page', 'GET',params, true)
}

/**
 * 获取楼栋的房间列表（根据新接口，楼栋下直接是房间）
 * @param {number} buildingId - 楼栋ID
 * @returns {Promise} 房间列表
 */
function getRoomsByBuilding(buildingId) {
  if (!buildingId) {
    return Promise.reject(new Error('楼栋ID不能为空'))
  }
  var param =
  {
    pageNum: 1,
    pageSize: 500,
    communityId: wx.getStorageSync('selectedCommunity').id,
    buildingId: buildingId

  }
  return REQUEST.request('/users-api/v1/community/room/page', 'GET', param, true)
}

/**
 * 设置默认房屋
 * @param {number} id - 房屋记录ID
 * @returns {Promise} 设置结果
 */
function setDefaultHouse(id) {
  if (!id) {
    return Promise.reject(new Error('房屋ID不能为空'))
  }

  var param = {
    id,
    isDefault: true
  }

  return REQUEST.request('/users-api/v1/member/room', 'PUT', param, true)
}

function formatHouseData(house) {
  var dictList = util.getDictByNameEn('resident_status')[0].children

  var dict = dictList.filter(item => item.nameEn === house.status)

  house.statusText = dict[0].nameCn
  house.statusClass = dict[0].cssClass


  return house
}


module.exports = {
  getHouseList,
  addHouse,
  updateHouse,
  deleteHouse,
  getBuildingsByCommunity,
  getRoomsByBuilding,
  setDefaultHouse,
  getHouseById,
  formatHouseData
}
