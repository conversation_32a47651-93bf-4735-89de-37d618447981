// complete-info.js
const util = require('@/utils/util.js')
const commApi = require('@/api/commApi.js')
const userApi = require('@/api/userApi.js');
Page({
  data: {
    apiUrl:wx.getStorageSync('apiUrl')+'/common-api/v1/file/',
    // 基本信息（从userInfo获取）
    name: '',
    phone: '',

    // 可编辑信息
    avatarUrl: '',
    gender: '',
    genderIndex: -1,
    birthday: '',
    certificateType: '',
    certificateTypeIndex: -1,
    idCardNumber: '',

    // 选项数据（从字典获取）
    genderOptions: [],
    certificateTypes: [],


    birthdayValid: false,
    birthdayError: false,
    certificateNumberValid: false,
    certificateNumberError: false,

    // 提交状态
    submitting: false
  },

  onLoad: function (options) {
    this.loadDictionaries()
    this.loadUserInfo()
  },

  // 加载字典数据
  loadDictionaries: function () {
    try {
      // 获取性别字典
      const genderDict = util.getDictByNameEn('gender')
      if (genderDict && genderDict[0] && genderDict[0].children) {
        this.setData({
          genderOptions: genderDict[0].children
        })
      }

      // 获取证件类型字典
      const certificateDict = util.getDictByNameEn('certificate_type')
      if (certificateDict && certificateDict[0] && certificateDict[0].children) {
        this.setData({
          certificateTypes: certificateDict[0].children
        })
      }
    } catch (error) {
      console.error('加载字典数据失败:', error)
      // 使用默认数据
      this.setData({
        genderOptions: [
          { nameCn: '男', nameEn: 'male' },
          { nameCn: '女', nameEn: 'female' }
        ],
        certificateTypes: [
          { nameCn: '身份证', nameEn: 'id_card' },
          { nameCn: '护照', nameEn: 'passport' },
          { nameCn: '港澳通行证', nameEn: 'hk_macau_pass' },
          { nameCn: '台湾通行证', nameEn: 'taiwan_pass' },
          { nameCn: '军官证', nameEn: 'military_id' },
          { nameCn: '其他', nameEn: 'other' }
        ]
      })
    }
  },

  // 加载用户信息
  loadUserInfo: function () {

    userApi.getUserInfo()
      .then(res => {

        if (res.code == 0) {
          const userInfo = res.data

          this.setData({
            name: userInfo.userName || '',
            phone: userInfo.phone || '',
            avatarUrl: this.data.apiUrl+userInfo.avatarUrl,
            gender: userInfo.gender || '',
            birthday: userInfo.birthday ? this.formatDate(userInfo.birthday) : '',
            certificateType: userInfo.certificateType || '',
            idCardNumber: userInfo.idCardNumber || ''
          })

          // 设置性别选择器索引
          if (userInfo.gender && this.data.genderOptions.length > 0) {
            const genderIndex = this.data.genderOptions.findIndex(item => item.nameEn === userInfo.gender)
            this.setData({ genderIndex: genderIndex >= 0 ? genderIndex : -1 })
          }

          // 设置证件类型选择器索引
          if (userInfo.certificateType && this.data.certificateTypes.length > 0) {
            const certificateTypeIndex = this.data.certificateTypes.findIndex(item => item.nameEn === userInfo.certificateType)
            this.setData({ certificateTypeIndex: certificateTypeIndex >= 0 ? certificateTypeIndex : -1 })
          }

        } else {

          wx.showToast({
            title: '获取个人信息失败' + (res.errorMessage ? (',' + res.errorMessage) : ''),
            icon: 'error',
            duration: 1000
          });

        }
      })
      .catch(err => {
        console.log('获取个人信息失败', err);
        // 本地测试环境，模拟成功
        wx.showToast({
          title: '获取个人信息失败' + (err.errorMessage ? (',' + err.errorMessage) : ''),
          icon: 'error',
          duration: 1000
        });

      });
  },

  // 格式化日期
  formatDate: function (dateString) {
    if (!dateString) return ''
    const date = new Date(dateString)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  },

  // 选择头像
  chooseAvatar: function () {
    wx.showActionSheet({
      itemList: ['从相册选择', '拍照'],
      success: (res) => {
        const sourceType = res.tapIndex === 0 ? ['album'] : ['camera']

        wx.chooseImage({
          count: 1,
          sizeType: ['compressed'],
          sourceType: sourceType,
          success: (res) => {
            this.setData({
              avatarUrl: res.tempFilePaths[0]
            })

            // 这里可以上传到服务器
            this.uploadAvatar(res.tempFilePaths[0])
          }
        })
      }
    })
  },

  // 上传头像
  uploadAvatar: function (filePath) {
    wx.showLoading({
      title: '上传中...',
      mask: true
    })

    commApi.upLoadFile(filePath)
      .then((result) => {
        wx.hideLoading()
        wx.showToast({
          title: '头像上传成功',
          icon: 'success'
        })
        // 保存服务器路径
        this.setData({
          avatarServerPath: result.data || ''
        })
      })
      .catch((error) => {
        wx.hideLoading()
        console.log('头像上传失败', error)
        wx.showToast({
          title: '头像上传失败',
          icon: 'none'
        })
      })
  },

  // 性别选择
  onGenderChange: function (e) {
    const index = e.detail.value
    const selectedGender = this.data.genderOptions[index]
    this.setData({
      genderIndex: index,
      gender: selectedGender ? selectedGender.nameEn : ''
    })
  },

  // 生日选择
  onBirthdayChange: function (e) {
    this.setData({
      birthday: e.detail.value
    })
    this.validateBirthday()
  },

  // 验证生日
  validateBirthday: function () {
    const birthday = this.data.birthday
    if (!birthday) {
      this.setData({
        birthdayValid: false,
        birthdayError: false
      })
      return false
    }

    const birthDate = new Date(birthday)
    const today = new Date()
    const age = today.getFullYear() - birthDate.getFullYear()

    const isValid = age >= 0 && age <= 150
    this.setData({
      birthdayValid: isValid,
      birthdayError: !isValid
    })
    return isValid
  },

  // 证件类型选择
  onCertificateTypeChange: function (e) {
    const index = e.detail.value
    const selectedType = this.data.certificateTypes[index]
    this.setData({
      certificateTypeIndex: index,
      certificateType: selectedType ? selectedType.nameEn : ''
    })
  },

  // 证件号码输入
  onIdCardNumberInput: function (e) {
    const idCardNumber = e.detail.value
    this.setData({ idCardNumber })
    this.validateCertificateNumber()
  },

  // 验证证件号码
  validateCertificateNumber: function () {
    const idCardNumber = this.data.idCardNumber.trim()
    const certificateType = this.data.certificateType

    let isValid = false

    if (certificateType === 'id_card') {
      isValid = /^\d{17}[\dXx]$/.test(idCardNumber)
    } else if (certificateType === 'passport') {
      isValid = /^[A-Za-z0-9]{6,20}$/.test(idCardNumber)
    } else {
      isValid = idCardNumber.length >= 6
    }

    this.setData({
      certificateNumberValid: isValid,
      certificateNumberError: idCardNumber.length > 0 && !isValid
    })
    return isValid
  },

  // 保存信息
  saveInfo: function () {
    // 验证表单
    let isValid = true


    if (this.data.birthday && !this.validateBirthday()) {
      isValid = false
    }

    if (this.data.idCardNumber && !this.validateCertificateNumber()) {
      isValid = false
    }

    if (!isValid) {
      wx.showToast({
        title: '请检查输入信息',
        icon: 'none'
      })
      return
    }

    this.setData({ submitting: true })

    // 准备保存的数据
    const userInfo = wx.getStorageSync('userInfo') || {}
    const updateData = {
      ...userInfo,
      avatarUrl: this.data.avatarServerPath || this.data.avatarUrl,
      gender: this.data.gender,
      birthday: this.data.birthday,
      certificateType: this.data.certificateType,
      idCardNumber: this.data.idCardNumber
    }

    // 调用实名认证API
    userApi.supplementUserInfo(updateData)
      .then(res => {
        this.setData({ submitting: false })
         
        if (res.code == 0) {

          // // 保存到本地存储
          wx.setStorageSync('userInfo', updateData)
          setTimeout(() => {
            this.setData({ submitting: false })
            wx.showToast({
              title: '保存成功',
              icon: 'success'
            })

            setTimeout(() => {
              wx.navigateBack()
            }, 1500)
          }, 1000)

        } else {

          wx.showToast({
            title: '完善个人信息失败' + (res.errorMessage ? (',' + res.errorMessage) : ''),
            icon: 'error',
            duration: 1500
          });
        }
      })
      .catch(err => {
        console.log('完善个人信息失败', err);
        // 本地测试环境，模拟成功
        wx.showToast({
          title: '完善个人信息失败' + (err.errorMessage ? (',' + err.errorMessage) : ''),
          icon: 'error',
          duration: 1500
        });
        // 恢复按钮状态
        this.setData({ submitting: false });
      });

  }
})
