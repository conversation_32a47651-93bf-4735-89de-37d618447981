// complete-info.js
const util = require('../../../../utils/util.js')
const commApi = require('../../../../api/commApi.js')

Page({
  data: {
    // 基本信息（从userInfo获取）
    name: '',
    phone: '',
    idCard: '',
    
    // 可编辑信息
    nickname: '',
    avatarUrl: '/images/default-avatar.svg',
    gender: '',
    genderIndex: -1,
    birthday: '',
    certificateType: '',
    certificateTypeIndex: -1,
    certificateNumber: '',
    
    // 选项数据
    genderOptions: ['男', '女'],
    certificateTypes: ['身份证', '护照', '港澳通行证', '台湾通行证', '军官证', '其他'],
    
    // 表单验证状态
    nicknameValid: false,
    nicknameError: false,
    birthdayValid: false,
    birthdayError: false,
    certificateNumberValid: false,
    certificateNumberError: false,
    
    // 提交状态
    submitting: false
  },

  onLoad: function (options) {
    this.loadUserInfo()
  },

  // 加载用户信息
  loadUserInfo: function () {
    const userInfo = wx.getStorageSync('userInfo') || {}
    
    this.setData({
      name: userInfo.userName || '',
      phone: userInfo.phone || '',
      idCard: userInfo.idCardNumber || '',
      nickname: userInfo.nickName || '',
      avatarUrl: userInfo.avatarUrl || '/images/default-avatar.svg',
      gender: userInfo.gender || '',
      birthday: userInfo.birthday ? this.formatDate(userInfo.birthday) : '',
      certificateType: userInfo.certificateType || '身份证',
      certificateNumber: userInfo.certificateNumber || userInfo.idCardNumber || ''
    })

    // 设置选择器索引
    if (userInfo.gender) {
      const genderIndex = this.data.genderOptions.indexOf(userInfo.gender === 'man' ? '男' : '女')
      this.setData({ genderIndex })
    }

    if (userInfo.certificateType) {
      const certificateTypeIndex = this.data.certificateTypes.indexOf(userInfo.certificateType)
      this.setData({ certificateTypeIndex })
    }
  },

  // 格式化日期
  formatDate: function (dateString) {
    if (!dateString) return ''
    const date = new Date(dateString)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  },

  // 昵称输入
  onNicknameInput: function (e) {
    const nickname = e.detail.value
    this.setData({ nickname })
    this.validateNickname()
  },

  // 验证昵称
  validateNickname: function () {
    const nickname = this.data.nickname.trim()
    const isValid = nickname.length >= 2 && nickname.length <= 20
    this.setData({
      nicknameValid: isValid,
      nicknameError: nickname.length > 0 && !isValid
    })
    return isValid
  },

  // 选择头像
  chooseAvatar: function () {
    wx.showActionSheet({
      itemList: ['从相册选择', '拍照'],
      success: (res) => {
        const sourceType = res.tapIndex === 0 ? ['album'] : ['camera']
        
        wx.chooseImage({
          count: 1,
          sizeType: ['compressed'],
          sourceType: sourceType,
          success: (res) => {
            this.setData({
              avatarUrl: res.tempFilePaths[0]
            })
            
            // 这里可以上传到服务器
            this.uploadAvatar(res.tempFilePaths[0])
          }
        })
      }
    })
  },

  // 上传头像
  uploadAvatar: function (filePath) {
    wx.showLoading({
      title: '上传中...',
      mask: true
    })

    commApi.upLoadFile(filePath)
      .then((result) => {
        wx.hideLoading()
        wx.showToast({
          title: '头像上传成功',
          icon: 'success'
        })
        // 保存服务器路径
        this.setData({
          avatarServerPath: result.data || ''
        })
      })
      .catch((error) => {
        wx.hideLoading()
        console.log('头像上传失败', error)
        wx.showToast({
          title: '头像上传失败',
          icon: 'none'
        })
      })
  },

  // 性别选择
  onGenderChange: function (e) {
    const index = e.detail.value
    this.setData({
      genderIndex: index,
      gender: this.data.genderOptions[index]
    })
  },

  // 生日选择
  onBirthdayChange: function (e) {
    this.setData({
      birthday: e.detail.value
    })
    this.validateBirthday()
  },

  // 验证生日
  validateBirthday: function () {
    const birthday = this.data.birthday
    if (!birthday) {
      this.setData({
        birthdayValid: false,
        birthdayError: false
      })
      return false
    }

    const birthDate = new Date(birthday)
    const today = new Date()
    const age = today.getFullYear() - birthDate.getFullYear()
    
    const isValid = age >= 0 && age <= 150
    this.setData({
      birthdayValid: isValid,
      birthdayError: !isValid
    })
    return isValid
  },

  // 证件类型选择
  onCertificateTypeChange: function (e) {
    const index = e.detail.value
    this.setData({
      certificateTypeIndex: index,
      certificateType: this.data.certificateTypes[index]
    })
  },

  // 证件号码输入
  onCertificateNumberInput: function (e) {
    const certificateNumber = e.detail.value
    this.setData({ certificateNumber })
    this.validateCertificateNumber()
  },

  // 验证证件号码
  validateCertificateNumber: function () {
    const certificateNumber = this.data.certificateNumber.trim()
    const certificateType = this.data.certificateType
    
    let isValid = false
    
    if (certificateType === '身份证') {
      isValid = /^\d{17}[\dXx]$/.test(certificateNumber)
    } else if (certificateType === '护照') {
      isValid = /^[A-Za-z0-9]{6,20}$/.test(certificateNumber)
    } else {
      isValid = certificateNumber.length >= 6
    }
    
    this.setData({
      certificateNumberValid: isValid,
      certificateNumberError: certificateNumber.length > 0 && !isValid
    })
    return isValid
  },

  // 保存信息
  saveInfo: function () {
    // 验证表单
    let isValid = true
    
    if (this.data.nickname && !this.validateNickname()) {
      isValid = false
    }
    
    if (this.data.birthday && !this.validateBirthday()) {
      isValid = false
    }
    
    if (this.data.certificateNumber && !this.validateCertificateNumber()) {
      isValid = false
    }
    
    if (!isValid) {
      wx.showToast({
        title: '请检查输入信息',
        icon: 'none'
      })
      return
    }
    
    this.setData({ submitting: true })
    
    // 准备保存的数据
    const userInfo = wx.getStorageSync('userInfo') || {}
    const updateData = {
      ...userInfo,
      nickName: this.data.nickname,
      avatarUrl: this.data.avatarServerPath || this.data.avatarUrl,
      gender: this.data.gender === '男' ? 'man' : 'woman',
      birthday: this.data.birthday,
      certificateType: this.data.certificateType,
      certificateNumber: this.data.certificateNumber
    }
    
    // 保存到本地存储
    wx.setStorageSync('userInfo', updateData)
    
    setTimeout(() => {
      this.setData({ submitting: false })
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      })
      
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }, 1000)
  }
})
