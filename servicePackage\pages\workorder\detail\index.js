// pages/workorder/detail/index.js
const workOrderApi = require('@/api/workOrderApi.js')
const util = require('@/utils/util.js')

Page({
  data: {
    workOrder: null,
    loading: true,
    showCancelConfirm: false,
    workOrderStatus: [],
    workOrderType: [],
    apiUrl: ''
  },

  onLoad(options) {
    const { id } = options

    // 初始化API地址
    this.setData({
      apiUrl: wx.getStorageSync('apiUrl')
    })

    if (id) {
      this.getWorkOrderStatusDict()
      this.getWorkOrderTypeDict()
      this.loadWorkOrderDetail(id)
    } else {
      wx.showToast({
        title: '工单ID不存在',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  // 获取工单状态字典
  getWorkOrderStatusDict: function () {
    var workOrderStatus = util.getDictByNameEn('work_order_status')[0].children
    this.setData({
      workOrderStatus
    })
  },

  // 获取工单类型字典
  getWorkOrderTypeDict: function () {
    var workOrderType = util.getDictByNameEn('work_order_type')[0].children
    this.setData({
      workOrderType
    })
  },

  // 加载工单详情
  loadWorkOrderDetail(id) {
    this.setData({ loading: true })

    // 调用API获取工单详情
    workOrderApi.getWorkOrderDetail(id)
      .then(response => {
        const order = response.data
        console.log('工单详情数据:', order)

        // 处理工单数据
        const processedOrder = {
          ...order,
          // 处理图片字段 - 支持单个图片或逗号分隔的多个图片
          imageList: order.media ? order.media.split(',').filter(img => img.trim()) : [],
          // 获取状态显示名称
          statusName: this.getStatusName(order.status),
          // 获取类型显示名称
          typeName: this.getTypeName(order.type),
          // 获取区域类型显示名称
          regionTypeName: this.getRegionTypeName(order.regionType),
          // 格式化时间
          createTimeFormatted: this.formatTime(order.createTime),
          updateTimeFormatted: order.updateTime ? this.formatTime(order.updateTime) : '',
          // 处理报修人信息
          reporterInfo: {
            name: order.residentName || '未知',
            phone: order.residentPhone || '',
            address: order.residentAddress || ''
          }
        }

        this.setData({
          workOrder: processedOrder,
          loading: false
        })
      })
      .catch(error => {
        console.error('加载工单详情失败', error)
        this.setData({ loading: false })
        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        })
      })
  },

  // 获取状态显示名称
  getStatusName: function (statusEn) {
    const status = this.data.workOrderStatus.find(item => item.nameEn === statusEn)
    return status ? status.nameCn : statusEn
  },

  // 获取类型显示名称
  getTypeName: function (typeEn) {
    const type = this.data.workOrderType.find(item => item.nameEn === typeEn)
    return type ? type.nameCn : typeEn
  },

  // 获取区域类型显示名称
  getRegionTypeName: function (regionTypeEn) {
    // 区域类型映射
    const regionTypeMap = {
      'house': '房屋',
      'public_area': '公共区域'
    }
    return regionTypeMap[regionTypeEn] || regionTypeEn
  },

  // 格式化时间
  formatTime: function (timeStr) {
    if (!timeStr) return ''
    const date = new Date(timeStr)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  },

  // 显示取消确认对话框
  showCancelConfirm() {
    this.setData({ showCancelConfirm: true })
  },

  // 隐藏取消确认对话框
  hideCancelConfirm() {
    this.setData({ showCancelConfirm: false })
  },

  // 取消工单
  cancelOrder() {
    const { workOrder } = this.data

    // 调用API取消工单
    workOrderApi.cancelWorkOrder(workOrder.id)
      .then(res => {

        if(res.code==0)
        {
          this.hideCancelConfirm()

        // 重新加载工单详情
        this.loadWorkOrderDetail(workOrder.id)

        wx.showToast({
          title: '工单已取消',
          icon: 'success'
        })
        }else{
          this.hideCancelConfirm()

          wx.showToast({
            title: res.errorMessage || '操作失败，请重试',
            icon: 'none'
          })
        }
        
      })
      .catch(error => {
        console.error('取消工单失败', error)
        this.hideCancelConfirm()

        wx.showToast({
          title: error.errorMessage || '操作失败，请重试',
          icon: 'none'
        })
      })
  },

  // 导航到评价页面
  navigateToEvaluate() {
    const { workOrder } = this.data
    wx.navigateTo({
      url: `/servicePackage/pages/workorder/evaluate/index?id=${workOrder.id}`
    })
  },

  // 预览图片
  previewImage(e) {
    const { index } = e.currentTarget.dataset
    const { workOrder, apiUrl } = this.data

    if (workOrder.imageList && workOrder.imageList.length > 0) {
      // 处理图片URL，支持完整URL和相对路径
      const fullUrls = workOrder.imageList.map(img => {
        if (img.startsWith('http')) {
          return img // 已经是完整URL
        } else {
          return apiUrl + '/common-api/v1/file/' + img
        }
      })

      wx.previewImage({
        current: fullUrls[index],
        urls: fullUrls
      })
    }
  }

})
