
const REQUEST = require('../utils/request.js')

/**
 * 工单模块API接口
 * 基于接口文档：/users-api/v1/member/work-order
 */

// ==================== 工单管理 ====================

/**
 * 分页查询工单列表
 * @param {Object} params - 查询参数
 * @param {number} params.pageNum - 页码，默认1
 * @param {number} params.pageSize - 每页大小，默认10
 * @param {string} params.status - 工单状态（可选）
 * @param {string} params.type - 工单类型（可选）
 * @returns {Promise} 工单列表
 */
function getWorkOrderList(params = {}) {
  const queryParams = {
    pageNum: params.pageNum || 1,
    pageSize: params.pageSize || 10
  }

  // 添加可选参数
  if (params.status) {
    queryParams.status = params.status
  }
  if (params.type) {
    queryParams.type = params.type
  }

  return REQUEST.request('/users-api/v1/member/work-order/page', 'GET', queryParams, true)
}

/**
 * 获取工单详情
 * @param {number} id - 工单ID
 * @returns {Promise} 工单详情
 */
function getWorkOrderDetail(id) {
  if (!id) {
    return Promise.reject(new Error('工单ID不能为空'))
  }
  return REQUEST.request('/users-api/v1/member/work-order?id=' + id, 'GET', {}, true)
}

/**
 * 新增工单
 * @param {Object} data - 工单数据
 * @param {string} data.type - 工单类型
 * @param {string} data.userDescribe - 问题描述
 * @param {string} data.media - 图片列表（多个用逗号分隔）
 * @param {string} data.regionType - 区域类型
 * @param {string} data.region - 区域描述
 * @returns {Promise} 创建结果
 */
function createWorkOrder(data) {
  const requestData = {
    type: data.type,
    userDescribe: data.userDescribe,
    media: data.media || '',
    regionType: data.regionType,
    region: data.region,
    communityId: data.communityId,
  }

  return REQUEST.request('/users-api/v1/member/work-order', 'POST', requestData, true)
}

/**
 * 取消工单
 * @param {number} id - 工单ID
 * @returns {Promise} 取消结果
 */
function cancelWorkOrder(id) {
  if (!id) {
    return Promise.reject(new Error('工单ID不能为空'))
  }

  const requestData = {
    id: id
  }

  return REQUEST.request('/users-api/v1/member/work-order/cancel', 'PUT', requestData, true)
}

module.exports = {
  getWorkOrderList,
  getWorkOrderDetail,
  createWorkOrder,
  cancelWorkOrder
}