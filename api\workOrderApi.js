
取消工单
'/users-api/v1/member/work-order/cancel' put
入参
{
  "id": "1"
}


获取工单详情
'/users-api/v1/member/work-order?id='+id get
返回值参考
{
    "errorMessage": "string",
    "code": 1073741824,
    "data": {
      "id": 9007199254740991,
      "type": "string",
      "userDescribe": "string",
      "personDescribe": "string",
      "media": "string",
      "status": "string",
      "createTime": "2025-06-09T06:27:50.562Z",
      "updateTime": "2025-06-09T06:27:50.562Z",
      "residentId": 9007199254740991,
      "personId": 9007199254740991,
      "regionType": "string",
      "region": "string"
    }
  }


新增工单
'/users-api/v1/member/work-order' post
入参
{
    "type": "string", // 工单类型
    "userDescribe": "string",//问题描述
    "media": "string", //图片列表
    "regionType": "string",//区域类型
    "region": "string"//区域描述(我的房屋:获取我的默认房屋地址,公共区域:输入区域地址)
  }



  分页查询工单
  '/users-api/v1/member/work-order/page' get 
  返回值参考
  {
    "errorMessage": "string",
    "code": 1073741824,
    "data": {
      "total": 9007199254740991,
      "list": [
        {
          "id": 9007199254740991,
          "type": "string", //英文 去匹配字典值
          "userDescribe": "string",
          "personDescribe": "string",
          "media": "string",//图片列表 可能是单个图片,可能是逗号分隔的多个图片,参考其他页面实现图片显示的方法
          "status": "string", //英文 去匹配字典值
          "createTime": "2025-06-09T06:28:13.701Z",
          "updateTime": "2025-06-09T06:28:13.701Z",
          "residentId": 9007199254740991,
          "personId": 9007199254740991,
          "regionType": "string", //英文 去匹配字典值
          "region": "string" //英文 去匹配字典值
        }
      ],
      "pageNum": 1073741824,
      "pageSize": 1073741824,
      "size": 1073741824,
      "startRow": 9007199254740991,
      "endRow": 9007199254740991,
      "pages": 1073741824,
      "prePage": 1073741824,
      "nextPage": 1073741824,
      "isFirstPage": true,
      "isLastPage": true,
      "hasPreviousPage": true,
      "hasNextPage": true,
      "navigatePages": 1073741824,
      "navigatepageNums": [
        1073741824
      ],
      "navigateFirstPage": 1073741824,
      "navigateLastPage": 1073741824
    }
  }