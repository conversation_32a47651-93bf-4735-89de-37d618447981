<!-- pages/workorder/detail/index.wxml -->
<view class="container">
  <!-- 加载中提示 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 工单详情内容 -->
  <scroll-view scroll-y class="detail-content" wx:else>
    <!-- 工单状态卡片 -->
    <view class="status-card status-{{workOrder.status}}">
      <view class="status-icon-container">
        <image
          src="/images/icons/status-{{workOrder.status}}.svg"
          class="status-icon"
        />
      </view>
      <view class="status-info">
        <view class="status-name">{{workOrder.statusName}}</view>
        <view class="status-desc">
          <block wx:if="{{workOrder.status === 'wait_process'}}">
            工单已提交，等待处理
          </block>
          <block wx:elif="{{workOrder.status === 'processing'}}">
            工单正在处理中
          </block>
          <block wx:elif="{{workOrder.status === 'completed'}}">
            工单已完成
          </block>
          <block wx:elif="{{workOrder.status === 'cancelled'}}">
            工单已取消
          </block>
        </view>
      </view>
    </view>

    <!-- 工单基本信息 -->
    <view class="detail-card">
      <view class="card-title">工单信息</view>
      <view class="info-item">
        <view class="info-label">工单编号</view>
        <view class="info-value">{{workOrder.id}}</view>
      </view>
      <view class="info-item">
        <view class="info-label">工单类型</view>
        <view class="info-value">{{workOrder.typeName}}</view>
      </view>
      <view class="info-item">
        <view class="info-label">创建时间</view>
        <view class="info-value">{{workOrder.createTime}}</view>
      </view>
      <view class="info-item" wx:if="{{workOrder.updateTime}}">
        <view class="info-label">更新时间</view>
        <view class="info-value">{{workOrder.updateTime}}</view>
      </view>
    </view>

    <!-- 问题描述 -->
    <view class="detail-card">
      <view class="card-title">问题描述</view>
      <view class="description-text">{{workOrder.userDescribe}}</view>

      <!-- 问题图片 -->
      <view class="image-list" wx:if="{{workOrder.imageList && workOrder.imageList.length > 0}}">
        <view class="image-item" wx:for="{{workOrder.imageList}}" wx:key="index">
          <image
            src="{{item.startsWith('http') ? item : (apiUrl + '/common-api/v1/file/' + item)}}"
            class="problem-image"
            mode="aspectFill"
            bindtap="previewImage"
            data-index="{{index}}"
          />
        </view>
      </view>

      <!-- 无图片提示 -->
      <view class="no-images" wx:if="{{!workOrder.imageList || workOrder.imageList.length === 0}}">
        <text class="no-images-text">暂无图片</text>
      </view>
    </view>

    <!-- 报修地址 -->
    <view class="detail-card">
      <view class="card-title">报修地址</view>
      <view class="address-type">
        {{workOrder.regionTypeName}}
      </view>
      <view class="address-value">{{workOrder.region}}</view>
    </view>

    <!-- 报修人信息 -->
    <view class="detail-card">
      <view class="card-title">报修人信息</view>
      <view class="reporter-info">
        <view class="reporter-avatar">
          <image src="/images/icons/user.svg" class="avatar-icon" />
        </view>
        <view class="reporter-detail">
          <view class="reporter-name">{{workOrder.reporterInfo.name}}</view>
          <view class="reporter-role">住户</view>
        </view>
      </view>
      <view class="info-item" wx:if="{{workOrder.reporterInfo.phone}}">
        <view class="info-label">手机号码</view>
        <view class="info-value">{{workOrder.reporterInfo.phone}}</view>
      </view>
      <view class="info-item" wx:if="{{workOrder.reporterInfo.address}}">
        <view class="info-label">房屋地址</view>
        <view class="info-value">{{workOrder.reporterInfo.address}}</view>
      </view>
    </view>

    <!-- 处理信息 -->
    <view class="detail-card" wx:if="{{workOrder.personDescribe}}">
      <view class="card-title">处理信息</view>
      <view class="description-text">{{workOrder.personDescribe}}</view>
    </view>
  </scroll-view>

  <!-- 底部操作区域 -->
  <view class="bottom-actions fixed-bottom" wx:if="{{!loading && workOrder.status === 'wait_process'}}">
    <button class="action-btn cancel-btn" bindtap="showCancelConfirm">取消工单</button>
  </view>

  <!-- 已完成状态 -->
  <view class="bottom-actions" wx:if="{{!loading && workOrder.status === 'completed'}}">
    <button
      class="action-btn evaluate-btn"
      bindtap="navigateToEvaluate"
    >
      立即评价
    </button>
  </view>

  <!-- 取消确认对话框 -->
  <view class="modal-mask" wx:if="{{showCancelConfirm}}">
    <view class="modal-container">
      <view class="modal-title">确认取消</view>
      <view class="modal-content">确定要取消该工单吗？取消后将无法恢复。</view>
      <view class="modal-footer">
        <button class="modal-btn cancel" bindtap="hideCancelConfirm">再想想</button>
        <button class="modal-btn confirm" bindtap="cancelOrder">确认取消</button>
      </view>
    </view>
  </view>
</view>
