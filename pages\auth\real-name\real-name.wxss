/* 实名认证页面样式 */
page {
  background-color: #f4f5f7;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "PingFang SC", "Helvetica Neue", Arial, sans-serif;
  color: #252f3f;
  line-height: 1.5;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  box-sizing: border-box;
}

/* 内容区域 */
.content-area {
  flex: 1;
  padding: 10px;
  box-sizing: border-box;
  height: 100vh;
}

/* 卡片样式 */
.card {
  background-color: #ffffff;
  border-radius: 24px;
  padding: 24px;
  margin-bottom: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.icon-check, .icon-user {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 8px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.icon-check {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23FF9500' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M22 11.08V12a10 10 0 1 1-5.93-9.14'/%3E%3Cpath d='M22 4 12 14.01l-3-3'/%3E%3C/svg%3E");
}

.icon-user {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23FF9500' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2'/%3E%3Ccircle cx='12' cy='7' r='4'/%3E%3C/svg%3E");
}

.icon-property {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 8px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23FF9500' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M3 21h18'/%3E%3Cpath d='M5 21V7l8-4v18'/%3E%3Cpath d='M19 21V11l-6-4'/%3E%3C/svg%3E");
}



/* 进度条 */
.progress-card {
  animation: fadeIn 0.5s ease-out;
}

.progress-track {
  position: relative;
  margin-bottom: 12px;
}

.progress-track::before {
  content: '';
  position: absolute;
  top: 15px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #e5e7eb;
  z-index: 1;
}

.progress-track::after {
  content: '';
  position: absolute;
  top: 15px;
  left: 0;
  width: 33.3%;
  height: 2px;
  background-color: #FF9500;
  z-index: 2;
  transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.progress-steps {
  display: flex;
  justify-content: space-between;
  position: relative;
  z-index: 3;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 3;
}

.step-dot {
  width: 34px;
  height: 34px;
  border-radius: 50%;
  background-color: #ffffff;
  border: 2px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #6b7280;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.step.active .step-dot {
  background-color: #FF9500;
  border-color: #FF9500;
  color: white;
  box-shadow: 0 0 0 4px rgba(255, 149, 0, 0.2);
}

.step.completed .step-dot {
  background-color: #3aad57;
  border-color: #3aad57;
  color: white;
}

.step.completed .step-dot::after {
  content: '✓';
  font-size: 14px;
}

.step-text {
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
}

/* 表单区域 */
.form-card {
  animation: slideUp 0.5s ease-out;
}



/* 表单内容 */
.form-slide {
  width: 100%;
  opacity: 1;
  position: relative;
}

.form-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.form-subtitle {
  font-size: 15px;
  font-weight: 500;
  margin-bottom: 16px;
  color: #4b5563;
}

/* 认证方式选择器 */
.auth-method-selector {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
}

.auth-method {
  flex: 1;
  padding: 16px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  background-color: #ffffff;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.auth-method.active {
  border-color: #FF9500;
  background-color: rgba(255, 149, 0, 0.05);
}

.auth-method-text {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.auth-method-title {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
}

.auth-method.active .auth-method-title {
  color: #FF9500;
}

/* 表单区域标题 */
.form-section-title {
  display: flex;
  align-items: center;
  margin: 24px 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1a1b21;
  padding-bottom: 12px;
  border-bottom: 1px solid #e5e7eb;
}

/* 工作证照片上传样式 */
.work-card-upload-container {
  width: 100%;
}

.work-card-upload-area {
  width: 100%;
  min-height: 160px;
  border: 2px dashed #D1D5DB;
  border-radius: 12px;
  background-color: #F9FAFB;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.work-card-upload-area:hover {
  border-color: #FF9500;
  background-color: rgba(255, 149, 0, 0.02);
}

.work-card-upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 20px;
}

.work-card-upload-icon {
  margin-bottom: 12px;
  opacity: 0.8;
}

.work-card-upload-text {
  font-size: 16px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.work-card-upload-tip {
  font-size: 12px;
  color: #9CA3AF;
  line-height: 1.4;
}

.work-card-upload-with-image {
  position: relative;
  width: 100%;
  border-radius: 12px;
  overflow: hidden;
  background-color: #F3F4F6;
}

.work-card-uploaded-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  display: block;
}

.work-card-upload-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.work-card-upload-with-image:hover .work-card-upload-overlay {
  opacity: 1;
}

.work-card-upload-actions {
  display: flex;
  gap: 20px;
}

.work-card-upload-action {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(4px);
  cursor: pointer;
  transition: all 0.3s ease;
}

.work-card-upload-action:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.work-card-action-icon {
  margin-bottom: 4px;
}

.work-card-action-text {
  font-size: 12px;
  color: white;
  font-weight: 500;
}

/* 表单项 */
.form-item {
  margin-bottom: 20px;
}

.form-label {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #333333;
}

.required {
  color: #fa3e3e;
  margin-left: 4px;
}

.optional {
  color: #6b7280;
  font-size: 12px;
  margin-left: 4px;
  font-weight: normal;
}

.form-input {
  position: relative;
  width: 100%;
  height: 48px;
  border: 1px solid #d2d6dc;
  border-radius: 12px;
  background-color: #f9fafb;
  padding: 0 16px;
  display: flex;
  align-items: center;
  transition: all 0.3s;
  box-sizing: border-box;
}

.form-input input {
  flex: 1;
  height: 100%;
  font-size: 14px;
  color: #1f2937;
  width: 100%;
}

.form-input.valid {
  border-color: #3aad57;
  background-color: rgba(58, 173, 87, 0.05);
}

.form-input.error {
  border-color: #fa3e3e;
}

.form-input-with-btn {
  position: relative;
  padding-right: 80px;
}

/* 带验证码的输入框 */
.form-input-with-code {
  position: relative;
  width: 100%;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  transition: all 0.3s;
}

.phone-input {
  flex: 1;
  height: 100%;
  border: 1px solid #d2d6dc;
  border-radius: 12px;
  background-color: #f9fafb;
  padding: 0 16px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
}

.phone-input input {
  flex: 1;
  height: 100%;
  font-size: 14px;
  color: #1f2937;
}

.form-input-with-code.valid .phone-input {
  border-color: #3aad57;
  background-color: rgba(58, 173, 87, 0.05);
}

.form-input-with-code.error .phone-input {
  border-color: #fa3e3e;
}

.verify-code-btn {
  height: 100%;
  min-width: 110px;
  background-color: #FF9500;
  color: white;
  font-size: 14px;
  border-radius: 12px;
  border: none;
  padding: 0 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.verify-code-btn.sent {
  background-color: #9ca3af;
}

.verify-code-btn[disabled] {
  background-color: #d1d5db;
  color: #9ca3af;
}

.verify-code-btn-inside {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  height: 36px;
  width: 70px;
  background-color: transparent;
  color: #FF9500;
  font-size: 13px;
  border: none;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  transition: all 0.3s;
}

.verify-code-btn-inside.sent {
  color: #9ca3af;
}

.verify-code-btn-inside[disabled] {
  color: #d1d5db;
}

/* 文本域 */
.form-textarea {
  position: relative;
  width: 100%;
  border: 1px solid #d2d6dc;
  border-radius: 12px;
  background-color: #f9fafb;
  padding: 12px 16px;
  display: flex;
  flex-direction: column;
  transition: all 0.3s;
  box-sizing: border-box;
}

.form-textarea textarea {
  width: 100%;
  height: 100px;
  font-size: 15px;
  color: #1f2937;
  line-height: 1.5;
  box-sizing: border-box;
}

.textarea-counter {
  align-self: flex-end;
  font-size: 12px;
  color: #9ca3af;
  margin-top: 4px;
}

.check-icon {
  width: 20px;
  height: 20px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%233aad57' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20 6L9 17l-5-5'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.form-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #6b7280;
}

/* 选择器 */
.form-selector {
  width: 100%;
  min-height: 48px;
  border: 1px solid #d2d6dc;
  border-radius: 12px;
  background-color: #f9fafb;
  padding: 0 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s;
  box-sizing: border-box;
}

.form-selector.valid {
  border-color: #3aad57;
  background-color: rgba(58, 173, 87, 0.05);
}

.form-selector.error {
  border-color: #fa3e3e;
}

.placeholder {
  color: #9fa6b2;
  font-size: 14px;
}

.arrow-icon {
  font-size: 12px;
  color: #9fa6b2;
}

/* 上传区域 */
.upload-container {
  width: 100%;
}

.upload-area-empty {
  width: 100%;
  padding: 0;
  border: 1px solid #e5e7eb;
  border-radius: 12px; /* 增加圆角 */
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden; /* 确保内容不超出圆角边界 */
}

.upload-area-with-image {
  width: 100%;
  border-radius: 12px; /* 增加圆角，与空状态保持一致 */
  overflow: hidden;
  position: relative;
}

.upload-text {
  display: none; /* 隐藏重复的标题 */
}

.upload-placeholder {
  width: 100%;
  height: 150px; /* 减小高度 */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* 移除箭头图标样式 */
.upload-icon {
  display: none;
}

.upload-options {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 20px 0;
  background-color: #ffffff;
  /* 移除绝对定位，使其居中 */
  position: relative;
}

.upload-option {
  display: flex;
  flex-direction: column; /* 改为纵向排列 */
  align-items: center;
  justify-content: center;
  padding: 15px 20px;
  border-radius: 8px;
  transition: background-color 0.3s;
}

.upload-option:active {
  background-color: #f3f4f6;
}

.upload-option-divider {
  width: 1px;
  height: 50px; /* 增加高度 */
  background-color: #e5e7eb;
  margin: 0 15px;
}

.upload-option-icon {
  width: 32px; /* 增大图标 */
  height: 32px;
  margin-bottom: 8px; /* 添加底部间距 */
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.upload-option-text {
  font-size: 14px;
  color: #FF9500;
  text-align: center;
}

.album-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23FF9500' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='3' width='18' height='18' rx='2' ry='2'/%3E%3Ccircle cx='8.5' cy='8.5' r='1.5'/%3E%3Cpolyline points='21 15 16 10 5 21'/%3E%3C/svg%3E");
}

.camera-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23FF9500' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z'/%3E%3Ccircle cx='12' cy='13' r='4'/%3E%3C/svg%3E");
}

.delete-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ef4444' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='3 6 5 6 21 6'/%3E%3Cpath d='M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2'/%3E%3Cline x1='10' y1='11' x2='10' y2='17'/%3E%3Cline x1='14' y1='11' x2='14' y2='17'/%3E%3C/svg%3E");
}

.upload-tip {
  font-size: 12px;
  color: #6b7280;
  text-align: center;
  width: 100%;
  padding: 8px;
  background-color: #f9fafb;
  border-top: 1px solid #e5e7eb;
  margin-top: 10px; /* 添加顶部间距 */
}

.uploaded-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.upload-actions {
  display: flex;
  width: 100%;
  justify-content: space-around;
  background-color: rgba(0, 0, 0, 0.7);
  padding: 12px;
  position: absolute;
  bottom: 0;
  left: 0;
}

.upload-action {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.upload-action-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.upload-action-text {
  font-size: 12px;
  color: white;
}

/* 已被上面的样式替代，删除重复定义 */

/* 提交按钮 */
.submit-btn {
  width: 100%;
  height: 54px;
  background-color: #0072c6;
  color: white;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  border: none;
  margin-top: 32px;
  box-shadow: 0 4px 10px rgba(12, 142, 231, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.submit-btn:active {
  transform: translateY(2px);
  box-shadow: 0 2px 5px rgba(12, 142, 231, 0.2);
}

/* 隐私提示 */
.privacy-tip {
  margin-top: 20px;
  font-size: 12px;
  color: #6b7280;
  text-align: center;
  line-height: 1.6;
}

.link {
  color: #0072c6;
}

/* 选择器弹窗 */
.selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.4s, visibility 0.4s;
}

.selector-overlay.active {
  opacity: 1;
  visibility: visible;
}

.selector-container {
  width: 100%;
  background-color: white;
  border-top-left-radius: 24px;
  border-top-right-radius: 24px;
  overflow: hidden;
  transform: translateY(100%);
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 -10px 40px rgba(0, 0, 0, 0.2);
}

.selector-overlay.active .selector-container {
  transform: translateY(0);
}

.selector-header {
  padding: 16px;
  text-align: center;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 1px solid #e5e7eb;
  position: relative;
}

.close-btn {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #0072c6;
  font-size: 16px;
  font-weight: 500;
}

.selector-content {
  max-height: 350px;
}

.selector-item {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.selector-item.active {
  background-color: rgba(12, 142, 231, 0.05);
}

.selector-item-content {
  flex: 1;
}

.selector-item-title {
  font-size: 15px;
  font-weight: 500;
  margin-bottom: 4px;
}

.selector-item-subtitle {
  font-size: 12px;
}

.selector-item-subtitle.verified {
  color: #3aad57;
}

.selector-item-subtitle.unverified {
  color: #fa3e3e;
}

.selector-item-check {
  width: 20px;
  height: 20px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%230c8ee7' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20 6L9 17l-5-5'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.empty-tip {
  padding: 32px 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  width: 64px;
  height: 64px;
  margin-bottom: 16px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23d2d6dc' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2z'/%3E%3Cpath d='M9 22V12h6v10'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
  color: #6b7280;
}

.selector-footer {
  padding: 16px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  gap: 12px;
}

.cancel-btn {
  flex: 1;
  height: 48px;
  background-color: #f3f4f6;
  color: #4b5563;
  font-size: 16px;
  font-weight: 500;
  border-radius: 12px;
  border: none;
}

.confirm-btn {
  flex: 1;
  height: 48px;
  background-color: #FF9500;
  color: white;
  font-size: 16px;
  font-weight: 500;
  border-radius: 12px;
  border: none;
}

.confirm-btn[disabled] {
  background-color: #d1d5db;
  color: #9ca3af;
}

/* 身份选择样式 */
.identity-options {
  display: flex;
  gap: 12px;
  width: 100%;
}

.identity-option {
  flex: 1;
  height: 48px;
  border: 1px solid #d2d6dc;
  border-radius: 12px;
  background-color: #f9fafb;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #4b5563;
  transition: all 0.3s;
}

.identity-option.active {
  border-color: #FF9500;
  background-color: rgba(255, 149, 0, 0.05);
  color: #FF9500;
  font-weight: 500;
}

/* 房屋选择表单样式 */
.house-selector-form {
  padding: 16px;
}

/* 弹窗 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.4s, visibility 0.4s;
}

.dialog-overlay.active {
  opacity: 1;
  visibility: visible;
}

.dialog-container {
  width: 320px;
  background-color: white;
  border-radius: 24px;
  overflow: hidden;
  transform: scale(0.95);
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.dialog-overlay.active .dialog-container {
  transform: scale(1);
}

.dialog-content {
  padding: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.dialog-icon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background-color: #f0f7ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%230c8ee7' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2z'/%3E%3Cpath d='M9 22V12h6v10'/%3E%3C/svg%3E");
  background-size: 36px;
  background-repeat: no-repeat;
  background-position: center;
}

.dialog-icon.success {
  background-color: #f2fbf4;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%233aad57' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20 6L9 17l-5-5'/%3E%3C/svg%3E");
}

.dialog-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 8px;
}

.dialog-message {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 20px;
}

.dialog-footer {
  display: flex;
  border-top: 1px solid #e5e7eb;
}

.dialog-footer.single {
  border-top: 1px solid #e5e7eb;
}

.dialog-btn {
  flex: 1;
  padding: 16px;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
}

.dialog-btn.cancel {
  border-right: 1px solid #e5e7eb;
  color: #6b7280;
}

.dialog-btn.confirm {
  color: #FF9500;
  font-weight: 600;
}

/* 动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 认证方式选择器 */
.auth-method-selector {
  display: flex;
  gap: 12px;
  width: 100%;
  margin-bottom: 8px;
  justify-content: space-between;
}

.auth-method {
  width: calc(50% - 6px);
  height: 20px;
  border: 1px solid #d2d6dc;
  border-radius: 12px;
  background-color: #f9fafb;
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.auth-method.active {
  border-color: #FF9500;
  background-color: rgba(255, 149, 0, 0.05);
}

.auth-method.active::after {
  content: '';
  position: absolute;
  right: 0;
  bottom: 0;
  width: 24px;
  height: 24px;
  background-color: #FF9500;
  clip-path: polygon(100% 0, 0% 100%, 100% 100%);
}



.auth-method-text {
  display: flex;
  align-items: center;
  justify-content: center;
}

.auth-method-title {
  font-size: 16px;
  font-weight: 500;
  color: #1f2937;

}

.auth-method-desc {
  font-size: 12px;
  color: #6b7280;
  width: 100%;
  text-align: center;
}

/* 日期时间选择器 */
.date-selector {
  display: flex;
  gap: 8px;
  padding: 16px;
  overflow-x: auto;
  white-space: nowrap;
  border-bottom: 1px solid #e5e7eb;
}

.date-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 80px;
  border: 1px solid #d2d6dc;
  border-radius: 12px;
  padding: 8px 0;
  transition: all 0.3s;
  flex-shrink: 0;
}

.date-item.active {
  border-color: #FF9500;
  background-color: rgba(255, 149, 0, 0.05);
}

.date-weekday {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
}

.date-day {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 2px;
}

.date-month {
  font-size: 12px;
  color: #6b7280;
}

.time-selector {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  padding: 16px;
}

.time-slot {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60px;
  border: 1px solid #d2d6dc;
  border-radius: 12px;
  padding: 8px;
  transition: all 0.3s;
}

.time-slot.active {
  border-color: #FF9500;
  background-color: rgba(255, 149, 0, 0.05);
}

.time-slot.disabled {
  opacity: 0.5;
  background-color: #f3f4f6;
  cursor: not-allowed;
}

.time-text {
  font-size: 15px;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 4px;
}

.time-status {
  font-size: 12px;
  color: #ef4444;
}

.confirm-btn {
  width: 100%;
  height: 48px;
  background-color: #FF9500;
  color: white;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.confirm-btn[disabled] {
  background-color: #d1d5db;
  color: #9ca3af;
}


.siwtch-btn{
  position: absolute;
  right: 0;
}


/* 头像区域 */
.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 80rpx;
  border: 4rpx solid #e5e7eb;
  background: #f9fafb;
  overflow: hidden;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 50%;
}

.avatar:active .avatar-overlay {
  opacity: 1;
}

.camera-icon {
  font-size: 48rpx;
}

.avatar-tip {
  font-size: 24rpx;
  color: #6b7280;
  text-align: center;
}