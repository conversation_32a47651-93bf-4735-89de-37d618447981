// pages/workorder/list/index.js
const workOrderManager = require('@/utils/workorder-manager');

Page({
  data: {
    statusTabs: ['全部', '待处理', '处理中', '已完成', '已取消'],
    currentTab: 0,
    statusMap: {
      0: '',  // 全部
      1: 'pending',
      2: 'processing',
      3: 'completed',
      4: 'cancelled'
    },
    workOrders: [],
    filteredOrders: [],
    searchValue: '',
    currentPage: 1,
    pageSize: 10,
    totalPages: 1,
    loading: false,
    refreshing: false,
    showEmpty: false
  },

  onLoad() {
    this.getWorkOrderTypeDict();
    this.getWorkOrderStatusDict();


    // 初始化模拟数据（仅用于开发测试）
    workOrderManager.initMockData();

    // 加载工单数据
    this.loadWorkOrders();
  },

   //获取工单类型字典
  getWorkOrderTypeDict: function () {
    
    工单类型 work_order_type
    --nameCn维修 nameEn repair
    --nameCn投诉 nameEn complaint‌
    --nameCn建议 nameEn suggestion
    --nameCn其他 nameEn other

    var workOrderType = util.getDictByNameEn('work_order_type')[0].children
    this.setData({
      workOrderType
    })

  },
  //获取工单状态字典
  getWorkOrderStatusDict()
  {
    工单状态 work_order_status

        --nameCn待处理 nameEn padding
        --nameCn处理中 nameEn processing
        --nameCn已完成 nameEn completed
        --nameCn已取消 nameEn cancled
    
        var workOrderStatus = util.getDictByNameEn('work_order_status')[0].children
        this.setData({
          workOrderStatus
        })
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadWorkOrders();
  },

  // 页面相关事件处理函数--监听用户下拉动作
  onPullDownRefresh() {
    this.loadWorkOrders(true);
  },

  // 页面上拉触底事件的处理函数
  onReachBottom() {
    if (this.data.currentPage < this.data.totalPages) {
      this.loadMoreOrders();
    }
  },

  // 加载工单数据
  loadWorkOrders(refresh = false) {
    this.setData({ loading: true });

    // 调用工单管理器获取工单数据
    workOrderManager.getWorkOrders()
      .then(orders => {
        if (refresh) {
          wx.stopPullDownRefresh();
        }

        // 排序工单
        const sortedOrders = workOrderManager.sortWorkOrders(orders);

        // 更新数据
        this.setData({
          workOrders: sortedOrders,
          currentPage: 1,
          totalPages: Math.ceil(sortedOrders.length / this.data.pageSize),
          loading: false,
          refreshing: false
        });

        // 筛选工单
        this.filterWorkOrders();
      })
      .catch(error => {
        console.error('加载工单失败', error);
        this.setData({ loading: false, refreshing: false });
        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      });
  },

  // 筛选工单
  filterWorkOrders() {
    const { workOrders, currentTab, statusMap, searchValue, currentPage, pageSize } = this.data;

    // 根据状态和搜索条件筛选工单
    let filtered = workOrders;

    // 状态筛选
    if (currentTab > 0) {
      const status = statusMap[currentTab];
      filtered = filtered.filter(order => order.status === status);
    }

    // 搜索筛选
    if (searchValue) {
      const keyword = searchValue.toLowerCase();
      filtered = filtered.filter(order =>
        order.content.title.toLowerCase().includes(keyword) ||
        order.content.description.toLowerCase().includes(keyword) ||
        order.id.toLowerCase().includes(keyword)
      );
    }

    // 计算分页
    const totalPages = Math.ceil(filtered.length / pageSize);
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, filtered.length);
    const currentPageOrders = filtered.slice(startIndex, endIndex);

    // 更新数据
    this.setData({
      filteredOrders: currentPageOrders,
      totalPages,
      showEmpty: filtered.length === 0
    });
  },

  // 切换状态标签
  switchTab(e) {
    const tab = parseInt(e.currentTarget.dataset.tab);
    this.setData({ currentTab: tab, currentPage: 1 });
    this.filterWorkOrders();
  },

  // 搜索工单
  searchWorkOrders(e) {
    this.setData({ searchValue: e.detail.value, currentPage: 1 });
    this.filterWorkOrders();
  },

  // 加载更多工单
  loadMoreOrders() {
    const { currentPage, totalPages } = this.data;
    if (currentPage >= totalPages) {
      wx.showToast({
        title: '已加载全部数据',
        icon: 'none'
      });
      return;
    }

    this.setData({ currentPage: currentPage + 1 });
    this.filterWorkOrders();
  },

  // 置顶/取消置顶工单
  togglePinOrder(e) {
    const orderId = e.currentTarget.dataset.id;

    // 调用工单管理器切换置顶状态
    workOrderManager.togglePinOrder(orderId)
      .then(() => {
        // 重新加载工单
        this.loadWorkOrders();

        wx.showToast({
          title: '操作成功',
          icon: 'success'
        });
      })
      .catch(error => {
        console.error('置顶工单失败', error);
        wx.showToast({
          title: '操作失败，请重试',
          icon: 'none'
        });
      });

    // 阻止事件冒泡
    return false;
  },

  // 查看工单详情
  navigateToDetail(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/servicePackage/pages/workorder/detail/index?id=${orderId}`
    });

    // 阻止事件冒泡
    return false;
  },

  // 取消工单
  cancelOrder(e) {
    const orderId = e.currentTarget.dataset.id;

    wx.showModal({
      title: '确认取消',
      content: '确定要取消该工单吗？取消后将无法恢复。',
      success: res => {
        if (res.confirm) {
          // 调用工单管理器取消工单
          workOrderManager.cancelOrder(orderId)
            .then(() => {
              // 重新加载工单
              this.loadWorkOrders();
              wx.showToast({
                title: '工单已取消',
                icon: 'success'
              });
            })
            .catch(error => {
              console.error('取消工单失败', error);
              wx.showToast({
                title: error.message || '操作失败，请重试',
                icon: 'none'
              });
            });
        }
      }
    });

    // 阻止事件冒泡
    return false;
  },

  // 创建新工单
  createNewOrder() {
    wx.navigateTo({
      url: '/servicePackage/pages/repair/repair'
    });
  },



  // 导航到评价页面
  navigateToEvaluate(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/servicePackage/pages/workorder/evaluate/index?id=${orderId}`
    });

    // 阻止事件冒泡
    return false;
  }
});
