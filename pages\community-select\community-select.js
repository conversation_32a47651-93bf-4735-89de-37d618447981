// community-select.js
const commApi = require('../../api/communityApi.js');

Page({
  data: {
    searchKeyword: '',
    allList: [],
    filteredList: [],
    loading: true
  },

  onLoad: function () {
    // 加载小区列表
    this.loadCommunityList()
  },

  // 加载小区列表
  loadCommunityList: function () {
    this.setData({ loading: true })

    commApi.communityList({
      pageNum: 1,
      pageSize: 100 // 获取足够多的数据
    }).then(res => {
      console.log('小区列表数据：', res)

      if (res.code === 0 && res.data && res.data.list) {
        const communityList = res.data.list.map(item => ({
          id: item.id,
          communityName: item.communityName,
          address: item.address || '',
          distance: '', // 不显示距离
          lng: item.lng,
          lat: item.lat,
          orgId: item.orgId
        }))

        this.setData({
          allList: communityList,
          filteredList: communityList,
          loading: false
        })
      } else {
        console.error('获取小区列表失败：', res)
        wx.showToast({
          title: '获取小区列表失败',
          icon: 'none'
        })
        this.setData({ loading: false })
      }
    }).catch(err => {
      console.error('获取小区列表异常：', err)
      wx.showToast({
        title: '网络异常，请重试',
        icon: 'none'
      })
      this.setData({ loading: false })
    })
  },

  // 搜索输入
  onSearchInput: function (e) {
    const keyword = e.detail.value
    this.setData({
      searchKeyword: keyword
    })
    this.filterCommunities(keyword)
  },

  // 清除搜索
  clearSearch: function () {
    this.setData({
      searchKeyword: ''
    })
    this.filterCommunities('')
  },

  // 过滤小区列表
  filterCommunities: function (keyword) {
    if (!keyword) {
      this.setData({
        filteredList: this.data.allList
      })
      return
    }

    const filtered = this.data.allList.filter(item => {
      return item.communityName.indexOf(keyword) !== -1 || item.address.indexOf(keyword) !== -1
    })

    this.setData({
      filteredList: filtered
    })
  },

  // 选择小区
  selectCommunity: function (e) {
    const community = e.currentTarget.dataset.community
   
    // 保存到本地存储
    wx.setStorageSync('selectedCommunity', community)

    // 显示选择成功提示
    wx.showToast({
      title: `已选择 ${community.communityName}`,
      icon: 'none',
      duration: 1500
    })


    // 延迟返回上一页，让用户看到提示
    setTimeout(() => {
      wx.navigateBack({
        success: () => {
          // 通知上一页刷新
          const pages = getCurrentPages()
          const prevPage = pages[pages.length - 2]
          if (prevPage && prevPage.checkCommunitySelection) {
            prevPage.checkCommunitySelection()
          }
        }
      })
    }, 1500)
  }
})
