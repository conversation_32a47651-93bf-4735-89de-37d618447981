// repair.js
const util = require('@/utils/util.js')
const workOrderApi = require('@/api/workOrderApi.js')
const commApi = require('@/api/commApi.js')
const houseApi = require('@/api/houseApi.js')

Page({
  data: {
    workOrderType: [],
    // 工单类型
    selectedWorkOrderType: 'repair',

    //区域类型
    regionType: [],
    selectedRegionType: 'house',

    // 房产列表和选择
    houses: [],
    selectedHouse: null,
    selectedHouseIndex: 0,

    // 报修地址
    publicAddress: '',

    // 报修人信息
    userInfo: null,

    // 物业人员创建工单时的报修人信息
    isPropertyStaff: false,
    customReporterName: '',
    customReporterPhone: '',
    customReporterRoleIndex: 0,
    reporterRoleOptions: ['业主', '租户', '访客', '其他'],

    // 问题描述
    description: '',

    // 图片上传
    images: [],
    apiUrl: '',

    // 表单验证
    canSubmit: false,

    // 加载状态
    showLoading: false,
    loadingText: '提交中...',

    // 提示弹窗
    showAlert: false,
    alertTitle: '提示',
    alertMessage: '',




  },

  onLoad: function (options) {
    // 初始化图片访问路径
    this.setData({
      apiUrl: wx.getStorageSync('apiUrl')
    })

    // 检查是否已认证
    if (!util.checkAuthentication()) {
      util.showAuthModal()
    }

    // 检查是否是物业人员创建工单
    const isPropertyStaff = options && options.from === 'property';

    // 如果是物业人员，设置相应的默认值和UI状态
    if (isPropertyStaff) {
      this.setData({
        isPropertyStaff: true,
        // 物业人员可以直接提交，不需要验证房屋信息
        canSubmit: true
      });
    }

    // 获取工单类型字典
    this.getWorkOrderTypeDict()
    // 获取区域类型
    this.getRegionType()

    this.getMyHouseList()

    this.getUserInfo()

    // 初始化表单验证
    this.checkCanSubmit()
  },

  // 获取用户信息
  getUserInfo: function() {
    const userInfo = wx.getStorageSync('userInfo')
    console.log('userInfo', userInfo)

    this.setData({
      userInfo: userInfo
    })
  },

  // 获取我的房屋列表
  getMyHouseList: function() {
    houseApi.getHouseList().then(res => {
      console.log('房屋列表数据：', res)

      if (res.code === 0 && res.data && Array.isArray(res.data.list)) {
        const houses = res.data.list.map(house => ({
          id: house.id,
          roomId: house.roomId,
          residentTypeCn: this.formatResidentType(house),
          fullAddress: this.formatHouseAddress(house),
          address: this.formatHouseAddress(house),
          isDefault: house.isDefault || false,
          buildingNumber: house.buildingNumber,
          unitNumber: house.unitNumber,
          roomNumber: house.roomNumber
        }))

        // 按默认房屋优先排序
        const sortedHouses = houses.sort((a, b) => {
          if (a.isDefault && !b.isDefault) return -1
          if (!a.isDefault && b.isDefault) return 1
          return 0
        })

        // 设置默认选中的房屋
        const defaultHouse = sortedHouses.find(house => house.isDefault) || sortedHouses[0]
        const defaultIndex = sortedHouses.findIndex(house => house.id === defaultHouse?.id)

        this.setData({
          houses: sortedHouses,
          selectedHouse: defaultHouse,
          selectedHouseIndex: defaultIndex >= 0 ? defaultIndex : 0
        })

        console.log('sortedHouses', sortedHouses)
        console.log('selectedHouse', defaultHouse)

      } else {
        console.error('获取房屋列表失败：', res)
      }
    }).catch(err => {
      console.error('获取房屋列表异常：', err)
    })
  },

  // 格式化住户类型
  formatResidentType: function(house) {
    // 角色：owner(业主)、tenant(租户)、family(家庭成员)
    if (house.residentType === 'owner') {
      return '业主'
    } else if (house.residentType === 'tenant') {
      return '租户'
    } else if (house.residentType === 'family') {
      return '家庭成员'
    }
    return '住户'
  },

  // 选择房屋
  selectHouse: function(e) {
    const index = e.detail.value
    const selectedHouse = this.data.houses[index]

    this.setData({
      selectedHouseIndex: index,
      selectedHouse: selectedHouse
    })

    this.checkCanSubmit()
  },


  // 格式化房屋地址
  formatHouseAddress: function (house) {
    const parts = []
    if (house.buildingNumber) parts.push(house.buildingNumber)
    if (house.unitNumber) parts.push(house.unitNumber)
    if (house.roomNumber) parts.push(house.roomNumber)
    return parts.join(' ') || `房间${house.roomId}`
  },


  // 获取工单类型字典
  getWorkOrderTypeDict: function () {
    // 工单类型 work_order_type
    // --nameCn维修 nameEn repair
    // --nameCn投诉 nameEn complaint‌
    // --nameCn建议 nameEn suggestion
    // --nameCn其他 nameEn other

    var workOrderType = util.getDictByNameEn('work_order_type')[0].children
    this.setData({
      workOrderType
    })
  },

  // 获取区域类型
  getRegionType: function () {
    // 区域类型 region_type
    // --nameCn房屋 nameEn house
    // --nameCn公共区域 nameEn public_area

    var regionType = util.getDictByNameEn('region_type')[0].children
    this.setData({
      regionType
    })
  },

  // 选择工单类型
  selectType: function (e) {
    const type = e.currentTarget.dataset.type
    this.setData({
      selectedWorkOrderType: type
    })
    this.checkCanSubmit()
  },

  // 选择区域类型
  selectRegionType: function (e) {
    const type = e.currentTarget.dataset.type
    this.setData({
      selectedRegionType: type
    })
    this.checkCanSubmit()
  },



  // 输入公共区域地址
  inputPublicAddress: function (e) {
    this.setData({
      publicAddress: e.detail.value
    })
    this.checkCanSubmit()
  },

  // 获取当前位置
  getLocation: function () {
    const that = this

    // 显示加载中
    this.showLoading('获取位置中...')

    wx.getLocation({
      type: 'gcj02',
      success: function (res) {
        // 模拟地理编码服务，实际应调用地图API
        setTimeout(() => {
          const address = '阳光小区 中心花园'
          that.setData({
            publicAddress: address
          })
          that.hideLoading()
          that.showAlert('已获取当前位置')
          that.checkCanSubmit()
        }, 1000)
      },
      fail: function (err) {
        that.hideLoading()
        let errorMessage = '无法获取位置'
        if (err.errMsg.indexOf('auth deny') !== -1) {
          errorMessage = '用户拒绝了位置请求'
        }
        that.showAlert(errorMessage)
      }
    })
  },

  // 切换报修人信息展示
  toggleReporterInfo: function () {
    this.setData({
      showReporterInfo: !this.data.showReporterInfo
    })
  },

  // 输入问题描述
  inputDescription: function (e) {
    this.setData({
      description: e.detail.value
    })
    this.checkCanSubmit()
  },

  // 选择图片
  chooseImage: function () {
    const remainCount = 9 - this.data.images.length
    if (remainCount <= 0) {
      wx.showToast({
        title: '最多上传9张图片',
        icon: 'none'
      })
      return
    }

    wx.chooseMedia({
      count: remainCount,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 上传图片
        this.uploadImages(res.tempFiles)
      }
    })
  },

  // 上传图片
  uploadImages: function(tempFiles) {
    if (!tempFiles || tempFiles.length === 0) return

    const uploadPromises = tempFiles.map(file => {
      return commApi.upLoadFile(file.tempFilePath)
    })

    Promise.all(uploadPromises)
      .then(results => {
        // 提取上传成功的图片URL
        const urls = results.map(result => result.data || '').filter(url => url)

        // 更新图片列表
        this.setData({
          images: [...this.data.images, ...urls]
        })
      })
      .catch(err => {
        console.error('图片上传失败:', err)
        wx.showToast({
          title: '图片上传失败',
          icon: 'none'
        })
      })
  },

  // 预览图片
  previewImage: function (e) {
    const index = e.currentTarget.dataset.index
    const images = this.data.images

    // 处理图片URL，添加服务器地址前缀
    const fullImageUrls = images.map(image => {
      if (image.startsWith('http')) {
        return image // 已经是完整URL
      } else {
        const apiUrl = wx.getStorageSync('apiUrl')
        return apiUrl + '/common-api/v1/file/' + image
      }
    })

    wx.previewImage({
      current: fullImageUrls[index],
      urls: fullImageUrls
    })
  },

  // 删除图片
  deleteImage: function (e) {
    const index = e.currentTarget.dataset.index
    const images = this.data.images
    images.splice(index, 1)
    this.setData({
      images
    })
  },

  // 检查表单是否可提交
  checkCanSubmit: function () {
    // 如果是物业人员，只需要检查问题描述
    if (this.data.isPropertyStaff) {
      const canSubmit = !!this.data.description;
      this.setData({ canSubmit });
      return;
    }

    // 普通用户需要检查更多字段
    let canSubmit = true

    // 检查区域类型和地址
    if (this.data.selectedRegionType === 'house') {
      // 房屋类型需要选择房屋
      if (!this.data.selectedHouse) {
        canSubmit = false
      }
    } else if (this.data.selectedRegionType === 'public_area') {
      // 公共区域需要输入地址
      if (!this.data.publicAddress) {
        canSubmit = false
      }
    }

    // 检查问题描述
    if (!this.data.description) {
      canSubmit = false
    }

    this.setData({ canSubmit })
  },

  // 物业人员专用输入处理函数
  inputCustomReporterName: function (e) {
    this.setData({
      customReporterName: e.detail.value
    })
  },

  inputCustomReporterPhone: function (e) {
    this.setData({
      customReporterPhone: e.detail.value
    })
  },

  selectCustomReporterRole: function (e) {
    this.setData({
      customReporterRoleIndex: e.detail.value
    })
  },

  // 提交报修
  submitRepair: function () {
    if (!this.data.canSubmit) return

    // 检查是否已认证
    if (!util.checkAuthentication()) {
      util.showAuthModal()
      return
    }

    // 显示加载中
    this.showLoading('提交中...')

    // 直接提交工单，图片已经在选择时上传
    this.createWorkOrder()
  },

  // 创建工单
  createWorkOrder: function () {
    // 准备工单数据
    const workOrderData = {
      type: this.data.selectedWorkOrderType,
      userDescribe: this.data.description,
      media: this.data.images.join(','), // 图片已经上传，直接拼接
      regionType: this.getRegionTypeValue(),
      region: this.getRegionValue(),
      communityId: wx.getStorageSync('selectedCommunity').id,
    }

    console.log('提交工单数据:', workOrderData)
    debugger
    // 调用API创建工单
    workOrderApi.createWorkOrder(workOrderData)
      .then(res => {
        debugger
        if(res.code==0)
        {
          
          this.hideLoading()
          this.showAlert('工单提交成功', '提交成功')
  
          // 延迟返回上一页
          setTimeout(() => {
            wx.navigateBack()
          }, 1500)
        }else

        {
          this.hideLoading()
          this.showAlert(res.errorMessage?res.errorMessage+'工单提交失败，请重试':"")
        }

     

      })
      .catch(error => {
        console.error('工单创建失败:', error)
        this.hideLoading()
        this.showAlert('工单提交失败，请重试')
      })
  },

  // 获取区域值
  getRegionValue: function () {
    if (this.data.selectedRegionType === 'house') {
      // 我的房屋：获取选中房屋地址
      return this.data.selectedHouse ? this.data.selectedHouse.fullAddress : ''
    } else {
      // 公共区域：输入的区域地址
      return this.data.publicAddress
    }
  },

  // 获取区域类型值
  getRegionTypeValue: function () {
    return this.data.selectedRegionType
  },

  // 获取工单类型名称
  getTypeName: function (type) {
    const typeNames = {
      'repair': '维修',
      'complaint': '投诉',
      'suggestion': '建议',
      'other': '其他'
    }
    return typeNames[type] || '未知类型'
  },

  // 生成工单ID
  generateOrderId: function () {
    const date = new Date()
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
    return `WO${year}${month}${day}${random}`
  },

  // 计算截止时间
  calculateDeadline: function () {
    const now = new Date()
    const deadline = new Date(now)
    deadline.setDate(deadline.getDate() + 3) // 默认3天后截止
    return deadline.toISOString().replace('T', ' ').substring(0, 16)
  },

  // 显示加载中
  showLoading: function (text = '加载中...') {
    this.setData({
      showLoading: true,
      loadingText: text
    })
  },

  // 隐藏加载中
  hideLoading: function () {
    this.setData({
      showLoading: false
    })
  },

  // 显示提示弹窗
  showAlert: function (message, title = '提示') {
    this.setData({
      showAlert: true,
      alertTitle: title,
      alertMessage: message
    })
  },

  // 关闭提示弹窗
  closeAlert: function () {
    this.setData({
      showAlert: false
    })
  },

  // 阻止事件冒泡
  stopPropagation: function () {
    // 阻止事件冒泡
  }
})
