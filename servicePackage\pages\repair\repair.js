// repair.js
const util = require('@/utils/util.js')
const workOrderApi = require('@/api/workOrderApi.js')
const commApi = require('@/api/commApi.js')
const houseApi = require('@/api/houseApi.js')

Page({
  data: {
    workOrderType: [],
    // 工单类型
    selectedWorkOrderType: 'repair',

    //区域类型
    regionType: [],
    selectedRegionType: 'house',

    //我的房屋列表
    houses:[],

    // 报修地址
    addressType: 'my-address',
    myAddress: '阳光小区 2栋 3单元 502室',
    publicAddress: '',

    // 报修人信息
    showReporterInfo: false,
    reporterName: '张三',
    reporterType: '住户',
    reporterPhone: '138****1234',
    reporterRole: '业主',
    reporterAddress: '阳光小区 2栋 3单元 502室',

    // 物业人员创建工单时的报修人信息
    isPropertyStaff: false,
    customReporterName: '',
    customReporterPhone: '',
    customReporterRoleIndex: 0,
    reporterRoleOptions: ['业主', '租户', '访客', '其他'],

    // 问题描述
    description: '',

    // 图片上传
    images: [],

    // 表单验证
    canSubmit: false,

    // 加载状态
    showLoading: false,
    loadingText: '提交中...',

    // 提示弹窗
    showAlert: false,
    alertTitle: '提示',
    alertMessage: '',




  },

  onLoad: function (options) {
    // 检查是否已认证
    if (!util.checkAuthentication()) {
      util.showAuthModal()
    }




    // 检查是否是物业人员创建工单
    const isPropertyStaff = options && options.from === 'property';

    // 如果是物业人员，设置相应的默认值和UI状态
    if (isPropertyStaff) {
      this.setData({
        isPropertyStaff: true,
        reporterType: '物业人员',
        // 物业人员可以直接提交，不需要验证房屋信息
        canSubmit: true
      });

    }



    //获取工单类型字典
    this.getWorkOrderTypeDict()
    //获取区域类型
    this.getRegionType()

    this.getMyHouseList()

    this.getUserInfo()

    // 初始化表单验证
    this.checkCanSubmit()

  },

  getUserInfo()
  {
    var userInfo=wx.getStorageSync('userInfo')

    console.log('userInfo', userInfo)

  },


  getMyHouseList()
  { 
    debugger
    houseApi.getHouseList().then(res => {
      console.log('房屋列表数据：', res)
      debugger
      if (res.code === 0 && res.data && Array.isArray(res.data.list)) {
        const houses = res.data.list.map(house => ({
          id: house.id,
          roomId: house.roomId,
          residentTypeCn:this.formatResidentTyoe(house),
          fullAddress: this.formatHouseAddress(house),
          address: this.formatHouseAddress(house),
          isDefault: house.isDefault || false
        }))

        // 按默认房屋优先排序
        const sortedHouses = houses.sort((a, b) => {
          if (a.isDefault && !b.isDefault) return -1
          if (!a.isDefault && b.isDefault) return 1
          return 0
        })

        this.setData({
          houses: sortedHouses
        })
        debugger
        console.log('sortedHouses', sortedHouses)

      } else {
        console.error('获取房屋列表失败：', res)

      }
    }).catch(err => {
      console.error('获取房屋列表异常：', err)

    })

  },

  //格式化住户类型
  formatResidentTyoe(house)
  {
    //角色：owner(业主)、tenant(租户)、family(家庭成员)
    if(house.regionType=='owner')
    {
      return '业主'
    }
    else  if(house.regionType=='tenant')
    {
      return '租户'
    }else  if(house.regionType=='family')
    {
      return '家庭成员'
    }

    return
  },


  // 格式化房屋地址
  formatHouseAddress: function (house) {
    const parts = []
    if (house.buildingNumber) parts.push(house.buildingNumber)
    if (house.unitNumber) parts.push(house.unitNumber)
    if (house.roomNumber) parts.push(house.roomNumber)
    return parts.join(' ') || `房间${house.roomId}`
  },


  // 获取工单类型字典
  getWorkOrderTypeDict: function () {
    // 工单类型 work_order_type
    // --nameCn维修 nameEn repair
    // --nameCn投诉 nameEn complaint‌
    // --nameCn建议 nameEn suggestion
    // --nameCn其他 nameEn other

    var workOrderType = util.getDictByNameEn('work_order_type')[0].children
    this.setData({
      workOrderType
    })
  },

  // 获取区域类型
  getRegionType: function () {
    // 区域类型 region_type
    // --nameCn房屋 nameEn house
    // --nameCn公共区域 nameEn public_area

    var regionType = util.getDictByNameEn('region_type')[0].children
    this.setData({
      regionType
    })
  },

  // 选择工单类型
  selectType: function (e) {
    const type = e.currentTarget.dataset.type
    this.setData({
      selectedWorkOrderType: type
    })
    this.checkCanSubmit()
  },

  // 选择区域类型
  selectRegionType: function (e) {
    const type = e.currentTarget.dataset.type
    this.setData({
      selectedRegionType: type
    })
    this.checkCanSubmit()
  },

  // 选择地址类型
  selectAddressType: function (e) {
    const type = e.currentTarget.dataset.type
    this.setData({
      addressType: type
    })
    this.checkCanSubmit()
  },

  // 输入公共区域地址
  inputPublicAddress: function (e) {
    this.setData({
      publicAddress: e.detail.value
    })
    this.checkCanSubmit()
  },

  // 获取当前位置
  getLocation: function () {
    const that = this

    // 显示加载中
    this.showLoading('获取位置中...')

    wx.getLocation({
      type: 'gcj02',
      success: function (res) {
        // 模拟地理编码服务，实际应调用地图API
        setTimeout(() => {
          const address = '阳光小区 中心花园'
          that.setData({
            publicAddress: address
          })
          that.hideLoading()
          that.showAlert('已获取当前位置')
          that.checkCanSubmit()
        }, 1000)
      },
      fail: function (err) {
        that.hideLoading()
        let errorMessage = '无法获取位置'
        if (err.errMsg.indexOf('auth deny') !== -1) {
          errorMessage = '用户拒绝了位置请求'
        }
        that.showAlert(errorMessage)
      }
    })
  },

  // 切换报修人信息展示
  toggleReporterInfo: function () {
    this.setData({
      showReporterInfo: !this.data.showReporterInfo
    })
  },

  // 输入问题描述
  inputDescription: function (e) {
    this.setData({
      description: e.detail.value
    })
    this.checkCanSubmit()
  },

  // 选择图片
  chooseImage: function () {
    const that = this
    wx.chooseMedia({
      count: 9 - that.data.images.length,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: function (res) {
        // 获取图片临时路径
        const tempFiles = res.tempFiles
        const newImages = tempFiles.map(file => file.tempFilePath)

        // 更新图片列表
        that.setData({
          images: [...that.data.images, ...newImages]
        })
      }
    })
  },

  // 预览图片
  previewImage: function (e) {
    const index = e.currentTarget.dataset.index
    wx.previewImage({
      current: this.data.images[index],
      urls: this.data.images
    })
  },

  // 删除图片
  deleteImage: function (e) {
    const index = e.currentTarget.dataset.index
    const images = this.data.images
    images.splice(index, 1)
    this.setData({
      images
    })
  },

  // 检查表单是否可提交
  checkCanSubmit: function () {
    // 如果是物业人员，只需要检查问题描述
    if (this.data.isPropertyStaff) {
      const canSubmit = !!this.data.description;

      this.setData({
        canSubmit
      });
      return;
    }

    // 普通用户需要检查更多字段
    let canSubmit = true

    // 检查地址
    if (this.data.addressType === 'public-area' && !this.data.publicAddress) {
      canSubmit = false
    }

    // 检查问题描述
    if (!this.data.description) {
      canSubmit = false
    }

    this.setData({
      canSubmit
    })
  },

  // 物业人员专用输入处理函数
  inputCustomReporterName: function (e) {
    this.setData({
      customReporterName: e.detail.value
    })
  },

  inputCustomReporterPhone: function (e) {
    this.setData({
      customReporterPhone: e.detail.value
    })
  },

  selectCustomReporterRole: function (e) {
    this.setData({
      customReporterRoleIndex: e.detail.value
    })
  },

  // 提交报修
  submitRepair: function () {
    if (!this.data.canSubmit) return

    // 检查是否已认证
    if (!util.checkAuthentication()) {
      util.showAuthModal()
      return
    }

    // 显示加载中
    this.showLoading('提交中...')

    // 先上传图片，然后提交工单
    this.uploadImages().then((mediaUrls) => {
      this.createWorkOrder(mediaUrls)
    }).catch((error) => {
      console.error('图片上传失败:', error)
      this.hideLoading()
      this.showAlert('图片上传失败，请重试')
    })
  },

  // 上传图片
  uploadImages: function () {
    const images = this.data.images
    if (!images || images.length === 0) {
      return Promise.resolve('')
    }

    const uploadPromises = images.map(imagePath => {
      return commApi.upLoadFile(imagePath)
    })

    return Promise.all(uploadPromises).then(results => {
      // 提取上传成功的图片URL
      const mediaUrls = results.map(result => {
        if (result && result.data) {
          return result.data
        }
        return ''
      }).filter(url => url !== '')

      return mediaUrls.join(',')
    })
  },

  // 创建工单
  createWorkOrder: function (mediaUrls) {
    // 准备工单数据
    const workOrderData = {
      type: this.data.selectedWorkOrderType,
      userDescribe: this.data.description,
      media: mediaUrls,
      regionType: this.getRegionType(),
      region: this.getRegionValue()
    }

    console.log('提交工单数据:', workOrderData)

    // 调用API创建工单
    workOrderApi.createWorkOrder(workOrderData)
      .then(response => {
        console.log('工单创建成功:', response)
        this.hideLoading()
        this.showAlert('工单提交成功', '提交成功')

        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      })
      .catch(error => {
        console.error('工单创建失败:', error)
        this.hideLoading()
        this.showAlert('工单提交失败，请重试')
      })
  },

  // 获取区域值
  getRegionValue: function () {
    if (this.data.addressType === 'my-address') {
      // 我的房屋：获取默认房屋地址
      return this.data.myAddress
    } else {
      // 公共区域：输入的区域地址
      return this.data.publicAddress
    }
  },

  // 获取区域类型
  getRegionType: function () {
    if (this.data.addressType === 'my-address') {
      return 'house'
    } else {
      return 'public_area'
    }
  },

  // 获取工单类型名称
  getTypeName: function (type) {
    const typeNames = {
      'repair': '维修',
      'complaint': '投诉',
      'suggestion': '建议',
      'other': '其他'
    }
    return typeNames[type] || '未知类型'
  },

  // 生成工单ID
  generateOrderId: function () {
    const date = new Date()
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
    return `WO${year}${month}${day}${random}`
  },

  // 计算截止时间
  calculateDeadline: function () {
    const now = new Date()
    const deadline = new Date(now)
    deadline.setDate(deadline.getDate() + 3) // 默认3天后截止
    return deadline.toISOString().replace('T', ' ').substring(0, 16)
  },

  // 显示加载中
  showLoading: function (text = '加载中...') {
    this.setData({
      showLoading: true,
      loadingText: text
    })
  },

  // 隐藏加载中
  hideLoading: function () {
    this.setData({
      showLoading: false
    })
  },

  // 显示提示弹窗
  showAlert: function (message, title = '提示') {
    this.setData({
      showAlert: true,
      alertTitle: title,
      alertMessage: message
    })
  },

  // 关闭提示弹窗
  closeAlert: function () {
    this.setData({
      showAlert: false
    })
  },

  // 阻止事件冒泡
  stopPropagation: function () {
    // 阻止事件冒泡
  }
})
