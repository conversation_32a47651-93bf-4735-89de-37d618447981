// repair.js
const util = require('@/utils/util.js')

Page({
  data: {
    workOrderType: [],
    // 工单类型
    selectedWorkOrderType: 'repair',

    //区域类型
    regionType: [],
    selectedRegionType: 'house',



    // 报修地址
    addressType: 'my-address',
    myAddress: '阳光小区 2栋 3单元 502室',
    publicAddress: '',

    // 报修人信息
    showReporterInfo: false,
    reporterName: '张三',
    reporterType: '住户',
    reporterPhone: '138****1234',
    reporterRole: '业主',
    reporterAddress: '阳光小区 2栋 3单元 502室',

    // 物业人员创建工单时的报修人信息
    isPropertyStaff: false,
    customReporterName: '',
    customReporterPhone: '',
    customReporterRoleIndex: 0,
    reporterRoleOptions: ['业主', '租户', '访客', '其他'],

    // 问题描述
    description: '',

    // 图片上传
    images: [],

    // 表单验证
    canSubmit: false,

    // 加载状态
    showLoading: false,
    loadingText: '提交中...',

    // 提示弹窗
    showAlert: false,
    alertTitle: '提示',
    alertMessage: '',




  },

  onLoad: function (options) {
    // 检查是否已认证
    if (!util.checkAuthentication()) {
      util.showAuthModal()
    }




    // 检查是否是物业人员创建工单
    const isPropertyStaff = options && options.from === 'property';

    // 如果是物业人员，设置相应的默认值和UI状态
    if (isPropertyStaff) {
      this.setData({
        isPropertyStaff: true,
        reporterType: '物业人员',
        // 物业人员可以直接提交，不需要验证房屋信息
        canSubmit: true
      });

    }



    //获取工单类型字典
    this.getWorkOrderTypeDict()
    //获取区域类型
    this.getRegionType()


    // 初始化表单验证
    this.checkCanSubmit()



  },

   //获取工单类型字典
  getWorkOrderTypeDict: function () {
    
    工单类型 work_order_type
    --nameCn维修 nameEn repair
    --nameCn投诉 nameEn complaint‌
    --nameCn建议 nameEn suggestion
    --nameCn其他 nameEn other

    var workOrderType = util.getDictByNameEn('work_order_type')[0].children
    this.setData({
      workOrderType
    })

  },

//获取区域类型
  getRegionType: function () {

    区域类型 region_type
    --nameCn房屋 nameEn house
    --nameCn公共区域 nameEn public_area

    var regionType = util.getDictByNameEn('region_type')[0].children
    this.setData({
      regionType
    })
  },

  // 选择工单类型
  selectType: function (e) {
    const type = e.currentTarget.dataset.type
    this.setData({
      selectedWorkOrderType: type
    })
    this.checkCanSubmit()
  },

  // 选择地址类型
  selectAddressType: function (e) {
    const type = e.currentTarget.dataset.type
    this.setData({
      addressType: type
    })
    this.checkCanSubmit()
  },

  // 输入公共区域地址
  inputPublicAddress: function (e) {
    this.setData({
      publicAddress: e.detail.value
    })
    this.checkCanSubmit()
  },

  // 获取当前位置
  getLocation: function () {
    const that = this

    // 显示加载中
    this.showLoading('获取位置中...')

    wx.getLocation({
      type: 'gcj02',
      success: function (res) {
        // 模拟地理编码服务，实际应调用地图API
        setTimeout(() => {
          const address = '阳光小区 中心花园'
          that.setData({
            publicAddress: address
          })
          that.hideLoading()
          that.showAlert('已获取当前位置')
          that.checkCanSubmit()
        }, 1000)
      },
      fail: function (err) {
        that.hideLoading()
        let errorMessage = '无法获取位置'
        if (err.errMsg.indexOf('auth deny') !== -1) {
          errorMessage = '用户拒绝了位置请求'
        }
        that.showAlert(errorMessage)
      }
    })
  },

  // 切换报修人信息展示
  toggleReporterInfo: function () {
    this.setData({
      showReporterInfo: !this.data.showReporterInfo
    })
  },

  // 输入问题描述
  inputDescription: function (e) {
    this.setData({
      description: e.detail.value
    })
    this.checkCanSubmit()
  },

  // 选择图片
  chooseImage: function () {
    const that = this
    wx.chooseMedia({
      count: 9 - that.data.images.length,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: function (res) {
        // 获取图片临时路径
        const tempFiles = res.tempFiles
        const newImages = tempFiles.map(file => file.tempFilePath)

        // 更新图片列表
        that.setData({
          images: [...that.data.images, ...newImages]
        })
      }
    })
  },

  // 预览图片
  previewImage: function (e) {
    const index = e.currentTarget.dataset.index
    wx.previewImage({
      current: this.data.images[index],
      urls: this.data.images
    })
  },

  // 删除图片
  deleteImage: function (e) {
    const index = e.currentTarget.dataset.index
    const images = this.data.images
    images.splice(index, 1)
    this.setData({
      images
    })
  },

  // 检查表单是否可提交
  checkCanSubmit: function () {
    // 如果是物业人员，只需要检查问题描述
    if (this.data.isPropertyStaff) {
      const canSubmit = !!this.data.description;

      this.setData({
        canSubmit
      });
      return;
    }

    // 普通用户需要检查更多字段
    let canSubmit = true

    // 检查地址
    if (this.data.addressType === 'public-area' && !this.data.publicAddress) {
      canSubmit = false
    }

    // 检查问题描述
    if (!this.data.description) {
      canSubmit = false
    }

    this.setData({
      canSubmit
    })
  },

  // 物业人员专用输入处理函数
  inputCustomReporterName: function (e) {
    this.setData({
      customReporterName: e.detail.value
    })
  },

  inputCustomReporterPhone: function (e) {
    this.setData({
      customReporterPhone: e.detail.value
    })
  },

  selectCustomReporterRole: function (e) {
    this.setData({
      customReporterRoleIndex: e.detail.value
    })
  },

  // 提交报修
  submitRepair: function () {
    if (!this.data.canSubmit) return

    // 检查是否已认证
    if (!util.checkAuthentication()) {
      util.showAuthModal()
      return
    }

    // 显示加载中
    this.showLoading('提交中...')

    // 创建工单对象
    let workOrder = {
      id: this.generateOrderId(),
      type: this.data.selectedWorkOrderType,
      typeName: this.getTypeName(this.data.selectedWorkOrderType),
      status: 'pending',
      statusName: '待处理',
      priority: 'medium',
      priorityName: '中',
      createTime: new Date().toISOString().replace('T', ' ').substring(0, 16),
      deadline: this.calculateDeadline(),
      content: {
        title: this.data.description.substring(0, 20) + (this.data.description.length > 20 ? '...' : ''),
        description: this.data.description,
        images: this.data.images || []
      }
    }

    // 根据用户类型设置不同的报修人信息和地址
    if (this.data.isPropertyStaff) {
      // 物业人员创建的工单
      workOrder.reporter = {
        name: this.data.customReporterName || '物业代报',
        phone: this.data.customReporterPhone || '',
        type: 'resident',
        role: this.data.reporterRoleOptions[this.data.customReporterRoleIndex] || '业主',
        community: '阳光小区'
      }

      workOrder.repairAddress = {
        type: 'public-area',
        value: this.data.publicAddress
      }

      // 物业创建的工单默认优先级更高
      workOrder.priority = 'high'
      workOrder.priorityName = '高'

      // 标记为物业代报
      workOrder.createdByStaff = true
    } else {
      // 普通用户创建的工单
      workOrder.reporter = {
        name: this.data.reporterName,
        phone: this.data.reporterPhone,
        type: this.data.reporterType === '住户' ? 'resident' : 'staff',
        role: this.data.reporterRole === '业主' ? 'owner' : 'tenant',
        community: '阳光小区',
        building: this.data.myAddress.split(' ')[1] || '',
        unit: this.data.myAddress.split(' ')[2] || '',
        room: this.data.myAddress.split(' ')[3] || ''
      }

      workOrder.repairAddress = {
        type: this.data.addressType,
        value: this.data.addressType === 'my-address'
          ? this.data.myAddress
          : this.data.publicAddress
      }
    }

    console.log('创建的工单对象:', workOrder)

    // 模拟提交
    setTimeout(() => {
      this.hideLoading()
      this.showAlert('工单提交成功', '提交成功')

      // 延迟返回上一页
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }, 1500)
  },

  // 获取工单类型名称
  getTypeName: function (type) {
    const typeNames = {
      'repair': '维修',
      'complaint': '投诉',
      'suggestion': '建议',
      'other': '其他'
    }
    return typeNames[type] || '未知类型'
  },

  // 生成工单ID
  generateOrderId: function () {
    const date = new Date()
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
    return `WO${year}${month}${day}${random}`
  },

  // 计算截止时间
  calculateDeadline: function () {
    const now = new Date()
    const deadline = new Date(now)
    deadline.setDate(deadline.getDate() + 3) // 默认3天后截止
    return deadline.toISOString().replace('T', ' ').substring(0, 16)
  },

  // 显示加载中
  showLoading: function (text = '加载中...') {
    this.setData({
      showLoading: true,
      loadingText: text
    })
  },

  // 隐藏加载中
  hideLoading: function () {
    this.setData({
      showLoading: false
    })
  },

  // 显示提示弹窗
  showAlert: function (message, title = '提示') {
    this.setData({
      showAlert: true,
      alertTitle: title,
      alertMessage: message
    })
  },

  // 关闭提示弹窗
  closeAlert: function () {
    this.setData({
      showAlert: false
    })
  },

  // 阻止事件冒泡
  stopPropagation: function () {
    // 阻止事件冒泡
  }
})
