<!--complete-info.wxml-->
<view class="container">
  <scroll-view scroll-y class="content-area">
    <!-- 页面标题 -->
    <view class="page-title">完善个人信息</view>
    <view class="page-subtitle">完善您的个人资料，享受更好的服务体验</view>

    <!-- 基本信息展示 -->
    <view class="info-card">
      <view class="card-title">
        <text class="icon-info"></text>
        <text>基本信息</text>
      </view>

      <view class="info-item">
        <view class="info-label">姓名</view>
        <view class="info-value">{{name || '未填写'}}</view>
      </view>

      <view class="info-item">
        <view class="info-label">手机号</view>
        <view class="info-value">{{phone || '未填写'}}</view>
      </view>
    </view>

    <!-- 可编辑信息 -->
    <view class="form-card">
      <view class="card-title">
        <text class="icon-edit"></text>
        <text>个人资料</text>
      </view>

      <!-- 头像 -->
      <view class="form-item">
        <view class="form-label">头像</view>
        <view class="avatar-section">
          <view class="avatar" bindtap="chooseAvatar">
            <image src="{{avatarUrl}}" mode="aspectFill"></image>
            <view class="avatar-overlay">
              <text class="camera-icon">📷</text>
            </view>
          </view>
          <view class="avatar-tip">点击更换头像</view>
        </view>
      </view>


      <!-- 性别 -->
      <view class="form-item">
        <view class="form-label">
          <text>性别</text>
          <text class="optional">（选填）</text>
        </view>
        <picker bindchange="onGenderChange" value="{{genderIndex}}" range="{{genderOptions}}" range-key="nameCn">
          <view class="form-selector">
            <text class="{{gender ? '' : 'placeholder'}}">{{genderIndex >= 0 ? genderOptions[genderIndex].nameCn : '请选择性别'}}</text>
            <text class="arrow-icon">▼</text>
          </view>
        </picker>
      </view>

      <!-- 生日 -->
      <view class="form-item">
        <view class="form-label">
          <text>出生日期</text>
          <text class="optional">（选填）</text>
        </view>
        <picker mode="date" bindchange="onBirthdayChange" start="1900"  value="{{birthday}}" end="{{today}}">
          <view class="form-selector {{birthdayValid ? 'valid' : birthdayError ? 'error' : ''}}">
            <text class="{{birthday ? '' : 'placeholder'}}">{{birthday || '请选择出生日期'}}</text>
            <text class="arrow-icon">▼</text>
          </view>
        </picker>
        <view class="form-tip" wx:if="{{birthdayError}}">请选择有效的出生日期</view>
      </view>

      <!-- 证件类型 -->
      <view class="form-item">
        <view class="form-label">
          <text>证件类型</text>
          <text class="optional">（选填）</text>
        </view>
        <picker bindchange="onCertificateTypeChange" value="{{certificateTypeIndex}}" range="{{certificateTypes}}" range-key="nameCn">
          <view class="form-selector">
            <text class="{{certificateType ? '' : 'placeholder'}}">{{certificateTypeIndex >= 0 ? certificateTypes[certificateTypeIndex].nameCn : '请选择证件类型'}}</text>
            <text class="arrow-icon">▼</text>
          </view>
        </picker>
      </view>

      <!-- 证件号码 -->
      <view class="form-item">
        <view class="form-label">
          <text>证件号码</text>
          <text class="optional">（选填）</text>
        </view>
        <view class="form-input {{certificateNumberValid ? 'valid' : certificateNumberError ? 'error' : ''}}">
          <input type="text" placeholder="请输入证件号码" value="{{idCardNumber}}" bindinput="onIdCardNumberInput" bindblur="validateCertificateNumber" />
          <view class="check-icon" wx:if="{{certificateNumberValid}}"></view>
        </view>
        <view class="form-tip" wx:if="{{certificateNumberError}}">请输入有效的证件号码</view>
      </view>
    </view>

    <!-- 保存按钮 -->
    <view class="button-section">
      <button class="save-btn" bindtap="saveInfo" loading="{{submitting}}">
        {{submitting ? '保存中...' : '保存信息'}}
      </button>
    </view>

    <!-- 提示信息 -->
    <view class="notice-section">
      <view class="notice-title">温馨提示</view>
      <view class="notice-content">
        • 完善个人信息有助于我们为您提供更好的服务
        • 您的个人信息将严格保密，仅用于身份验证和服务提供
        • 标记为"选填"的信息可以暂时不填写
      </view>
    </view>
  </scroll-view>
</view>
